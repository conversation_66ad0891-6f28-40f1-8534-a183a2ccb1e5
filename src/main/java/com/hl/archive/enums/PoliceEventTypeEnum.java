package com.hl.archive.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 新警事件类型枚举
 * 定义新警时间轴中的各种事件类型
 */
@Getter
@AllArgsConstructor
public enum PoliceEventTypeEnum {

    /**
     * 社团活动
     */
    CLUB_ACTIVITY("club_activity", "社团活动", "参与的社团活动事件", "#1890ff", "team"),

    /**
     * 教育培训
     */
    TRAINING("training", "教育培训", "参与的教育培训事件", "#52c41a", "book"),

    /**
     * 网上考试
     */
    ONLINE_EXAM("online_exam", "网上考试", "参与的网上考试事件", "#faad14", "file-text"),

    /**
     * 表彰奖励
     */
    HONORS_AWARDS("honors_awards", "表彰奖励", "获得的表彰奖励事件", "#f5222d", "trophy"),

    /**
     * 婚丧嫁娶
     */
    WEDDING_FUNERAL("wedding_funeral", "婚丧嫁娶", "操办的婚丧嫁娶事件", "#722ed1", "heart"),

    /**
     * 年度考核
     */
    ANNUAL_ASSESSMENT("annual_assessment", "年度考核", "年度考核评估事件", "#13c2c2", "calendar"),

    /**
     * 月度考核
     */
    MONTHLY_ASSESSMENT("monthly_assessment", "月度考核", "月度考核评估事件", "#13c2c2", "calendar"),

    /**
     * 季度考核
     */
    QUARTERLY_ASSESSMENT("quarterly_assessment", "季度考核", "季度考核评估事件", "#13c2c2", "calendar"),

    /**
     * 训练记录
     */
    TRAINING_RECORDS("training_records", "训练记录", "训练档案记录事件", "#eb2f96", "fire"),

    /**
     * 职务变动
     */
    POSITION_CHANGE("position_change", "职务变动", "职务职级变动事件", "#fa8c16", "user"),

    /**
     * 学历教育
     */
    EDUCATION("education", "学历教育", "学历教育事件", "#2f54eb", "graduation-cap"),

    /**
     * 健康体检
     */
    HEALTH_CHECK("health_check", "健康体检", "健康体检事件", "#a0d911", "medicine-box"),

    /**
     * 出入境记录
     */
    TRAVEL_RECORD("travel_record", "出入境记录", "出入境记录事件", "#fa541c", "global"),

    /**
     * 其他事项
     */
    OTHER("other", "其他事项", "其他类型的事件", "#8c8c8c", "ellipsis");

    /**
     * 事件类型代码
     */
    private final String code;

    /**
     * 事件类型名称
     */
    private final String name;

    /**
     * 事件类型描述
     */
    private final String description;

    /**
     * 事件颜色（用于前端显示）
     */
    private final String color;

    /**
     * 事件图标（用于前端显示）
     */
    private final String icon;

    /**
     * 根据代码获取枚举
     */
    public static PoliceEventTypeEnum getByCode(String code) {
        if (code == null) {
            return OTHER;
        }
        for (PoliceEventTypeEnum eventType : values()) {
            if (eventType.getCode().equals(code)) {
                return eventType;
            }
        }
        return OTHER;
    }

    /**
     * 根据名称获取枚举
     */
    public static PoliceEventTypeEnum getByName(String name) {
        if (name == null) {
            return OTHER;
        }
        for (PoliceEventTypeEnum eventType : values()) {
            if (eventType.getName().equals(name)) {
                return eventType;
            }
        }
        return OTHER;
    }

    /**
     * 获取所有事件类型的代码列表
     */
    public static String[] getAllCodes() {
        PoliceEventTypeEnum[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有事件类型的名称列表
     */
    public static String[] getAllNames() {
        PoliceEventTypeEnum[] values = values();
        String[] names = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            names[i] = values[i].getName();
        }
        return names;
    }
}
