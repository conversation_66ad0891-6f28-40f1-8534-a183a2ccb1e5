package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.enums.PoliceEventTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 简化版时间轴事件DTO
 * 用于统一表示不同类型的事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "时间轴事件")
public class SimpleTimelineEventDTO {

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID")
    private String eventId;

    /**
     * 事件类型枚举
     */
    @ApiModelProperty(value = "事件类型枚举")
    private PoliceEventTypeEnum eventTypeEnum;

    /**
     * 事件类型名称
     */
    @ApiModelProperty(value = "事件类型名称")
    private String eventType;

    /**
     * 事件标题
     */
    @ApiModelProperty(value = "事件标题")
    private String title;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String description;

    /**
     * 事件时间
     */
    @ApiModelProperty(value = "事件时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    private String location;

    /**
     * 事件颜色
     */
    @ApiModelProperty(value = "事件颜色")
    private String color;

    /**
     * 事件图标
     */
    @ApiModelProperty(value = "事件图标")
    private String icon;
}
