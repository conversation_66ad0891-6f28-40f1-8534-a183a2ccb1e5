package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 简化版时间轴查询DTO
 */
@Data
@ApiModel(description = "时间轴查询参数")
public class SimpleTimelineQueryDTO {

    /**
     * 身份证号（必填）
     */
    @ApiModelProperty(value = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空")
    private String idCard;


}
