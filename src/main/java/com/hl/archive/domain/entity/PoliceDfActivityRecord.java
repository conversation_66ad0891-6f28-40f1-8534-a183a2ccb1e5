package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 登锋活动记录表
 */
@ApiModel(description = "登锋活动记录表")
@Data
@TableName(value = "police_df_activity_record", autoResultMap = true)
public class PoliceDfActivityRecord {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动类型
     */
    @TableField(value = "activity_type")
    @ApiModelProperty(value = "活动类型")
    private String activityType;

    /**
     * 活动标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value = "活动标题")
    private String title;

    /**
     * 活动内容或描述
     */
    @TableField(value = "content")
    @ApiModelProperty(value = "活动内容或描述")
    private String content;

    /**
     * 活动开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 活动地点
     */
    @TableField(value = "`location`")
    @ApiModelProperty(value = "活动地点")
    private String location;

    /**
     * 附件
     */
    @TableField(value = "files", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "附件")
    private JSONArray files;

    /**
     * 状态: 0-未开始 1-进行中 2-已完成
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态: 0-未开始 1-进行中 2-已完成")
    private Byte status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 是否删除: 0-否 1-是
     */
    @TableField(value = "is_deleted")
    @TableLogic
    @ApiModelProperty(value = "是否删除: 0-否 1-是")
    private Byte isDeleted;
}