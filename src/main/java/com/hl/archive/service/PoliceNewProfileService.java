package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.*;
import com.hl.archive.domain.entity.PoliceClubActivity;
import com.hl.archive.domain.entity.PoliceNewProfile;
import com.hl.archive.enums.TimelineEventTypeEnum;
import com.hl.archive.mapper.PoliceNewProfileMapper;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PoliceNewProfileService extends ServiceImpl<PoliceNewProfileMapper, PoliceNewProfile> {

    public Page<PoliceNewProfile> pageList(PoliceNewProfileQueryDTO requestDTO) {
        LambdaQueryWrapper<PoliceNewProfile> queryWrapper = Wrappers.<PoliceNewProfile>lambdaQuery();
        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(requestDTO.getOrganizationId())) {
                // 非320412000000时，截取前8位
                requestDTO.setOrganizationId(requestDTO.getOrganizationId().substring(0, 8));
                queryWrapper.like(PoliceNewProfile::getOrganizationId, requestDTO.getOrganizationId());
            }
        }
        if (StrUtil.isNotBlank(requestDTO.getQuery())) {
            queryWrapper.like(PoliceNewProfile::getName, requestDTO.getQuery());
        }
        if (StrUtil.isNotBlank(requestDTO.getIdCard())) {
            queryWrapper.eq(PoliceNewProfile::getIdCard, requestDTO.getIdCard());
        }
        queryWrapper.eq(StrUtil.isNotBlank(requestDTO.getUnitLeader()), PoliceNewProfile::getUnitLeader, requestDTO.getUnitLeader())
                .eq(StrUtil.isNotBlank(requestDTO.getRoleModel()), PoliceNewProfile::getRoleModel, requestDTO.getRoleModel());
        Page<PoliceNewProfile> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        // 检验标识
        checkMark(page.getRecords());
        return page;
    }


    private void checkMark(List<PoliceNewProfile> policeNewProfiles) {
        String card = UserUtils.getUser().getIdCard();
        for (PoliceNewProfile newProfile : policeNewProfiles) {
            String idCard = newProfile.getIdCard();
            if (card.equals(idCard)) {
                newProfile.setIsNewPolice(true);
            }
            String roleModel = newProfile.getRoleModel();
            if (card.equals(roleModel)) {
                newProfile.setIsRoleModel(true);
            }
        }
    }

    /**
     * 导出新警画像数据到Excel
     *
     * @param dto      查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportPoliceNewProfile(PoliceNewProfileQueryDTO dto, HttpServletResponse response) throws IOException {
        // 设置查询条件为导出所有数据
        dto.setLimit(Integer.MAX_VALUE);
        Page<PoliceNewProfile> page = this.pageList(dto);
        List<PoliceNewProfile> records = page.getRecords();

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 使用FastExcel导出
        FastExcel.write(response.getOutputStream(), PoliceNewProfile.class)
                .autoCloseStream(Boolean.FALSE)
                .sheet("新警画像信息导出")
                .doWrite(records);
    }

    public PoliceNewProfileRoleReturnDTO checkRole() {
        List<PoliceNewProfile> list = this.list();
        String idCard = UserUtils.getUser().getIdCard();
        PoliceNewProfileRoleReturnDTO returnDTO = new PoliceNewProfileRoleReturnDTO();
        for (PoliceNewProfile policeNewProfile : list) {

            if (idCard.equals(policeNewProfile.getIdCard())) {
                returnDTO.setIsNewPolice(true);
            }
            if (idCard.equals(policeNewProfile.getUnitLeader())) {
                returnDTO.setIsUnitLeader(true);
            }
            if (idCard.equals(policeNewProfile.getRoleModel())) {
                returnDTO.setIsRoleModel(true);
            }
        }
        return returnDTO;
    }

    public void exportPoliceNewProfileWord(PoliceNewProfileQueryDTO requestDTO, HttpServletResponse response) {

        try {
            String idCard = requestDTO.getIdCard();
            PoliceNewProfile one = this.getOne(Wrappers.<PoliceNewProfile>lambdaQuery().eq(PoliceNewProfile::getIdCard, idCard));

            XWPFTemplate template = XWPFTemplate.compile("conf/template/new_police_export.docx")
                    .render(one);
            response.setContentType("application/octet-stream");
            String fileName = URLEncoder.encode("新警画像", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            template.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
        } catch (IOException e) {
            log.error("导出Word失败", e);
        }
    }

    public void exportPoliceListWord(PoliceNewProfileQueryDTO requestDTO, HttpServletResponse response) {
        try {
            // 设置查询条件为导出所有数据
            requestDTO.setLimit(Integer.MAX_VALUE);
            Page<PoliceNewProfile> page = this.pageList(requestDTO);
            List<PoliceNewProfile> records = page.getRecords();
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();

            List<JSONObject> result = new ArrayList<>();
            records.forEach(r -> {
                JSONObject object = new JSONObject();
                String organizationId = r.getOrganizationId();

                object.put("organizationName", SsoCacheUtil.getOrganizationName(organizationId));
                object.put("unitLeader",SsoCacheUtil.getUserNameByIdCard(r.getUnitLeader()));
                object.put("roleModel",SsoCacheUtil.getUserNameByIdCard(r.getRoleModel()));
                object.put("name",r.getName());
                result.add(object);
            });

            Configure config = Configure.builder()
                    .bind("polices", policy)
                    .bind("labors", policy)
                    .useSpringEL()
                    .build();

            Map<String, Object> data = new HashMap<>();
            data.put("polices", result);


            XWPFTemplate template = XWPFTemplate.compile("conf/template/new_police_list.docx", config)
                    .render(data);
            response.setContentType("application/octet-stream");
            String fileName = URLEncoder.encode("新警画像", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            template.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
        } catch (IOException e) {
            log.error("导出Word失败", e);
        }
    }


//    @EventListener(ApplicationReadyEvent.class)
    public void cleanOrganization(){
        this.list().forEach(r -> {
            String userOrgIdByIdCard = SsoCacheUtil.getUserOrgIdByIdCard(r.getIdCard());
            r.setUnitLeader(userOrgIdByIdCard);
            this.updateById(r);
        });
    }


}
