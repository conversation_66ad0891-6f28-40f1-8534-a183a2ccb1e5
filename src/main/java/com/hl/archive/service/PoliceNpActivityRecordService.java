package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.PoliceNpActivityRecordQueryDTO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceNpActivityRecordMapper;
import com.hl.archive.domain.entity.PoliceNpActivityRecord;
@Service
public class PoliceNpActivityRecordService extends ServiceImpl<PoliceNpActivityRecordMapper, PoliceNpActivityRecord> {

    public Page<PoliceNpActivityRecord> pageList(PoliceNpActivityRecordQueryDTO request) {
        LambdaQueryWrapper<PoliceNpActivityRecord> queryWrapper = Wrappers.<PoliceNpActivityRecord>lambdaQuery()
                .eq(StrUtil.isNotBlank(request.getIdCard()), PoliceNpActivityRecord::getIdCard, request.getIdCard());
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(request.getOrganizationId())) {
                queryWrapper.like(PoliceNpActivityRecord::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }else {
                queryWrapper.eq(PoliceNpActivityRecord::getOrganizationId, request.getOrganizationId());
            }
        }
        Page<PoliceNpActivityRecord> page = this.page(Page.of(request.getPage(), request.getLimit()), queryWrapper);
        return page;


    }
}
