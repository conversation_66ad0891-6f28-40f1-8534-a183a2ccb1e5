package com.hl.archive.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceDfActivityRecordRequestDTO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceDfActivityRecordMapper;
import com.hl.archive.domain.entity.PoliceDfActivityRecord;
@Service
public class PoliceDfActivityRecordService extends ServiceImpl<PoliceDfActivityRecordMapper, PoliceDfActivityRecord> {

    public Page<PoliceDfActivityRecord> pageList(PoliceDfActivityRecordRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceDfActivityRecord> queryWrapper = Wrappers.<PoliceDfActivityRecord>lambdaQuery();
        if (requestDTO.getStartTime() != null) {
            queryWrapper.ge(PoliceDfActivityRecord::getStartTime, requestDTO.getStartTime());
        }
        if (requestDTO.getEndTime() != null) {
            queryWrapper.le(PoliceDfActivityRecord::getEndTime, requestDTO.getEndTime());
        }
        Page<PoliceDfActivityRecord> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        return page;
    }
}
