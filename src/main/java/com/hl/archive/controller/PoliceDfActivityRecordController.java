package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceDfActivityRecordRequestDTO;
import com.hl.archive.domain.entity.PoliceDfActivityRecord;
import com.hl.archive.service.PoliceDfActivityRecordService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/dfActivityRecord")
@RequiredArgsConstructor
@Api(tags = "登锋活动记录")
public class PoliceDfActivityRecordController {

    private final PoliceDfActivityRecordService policeDfActivityRecordService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceDfActivityRecord>> page(@RequestBody PoliceDfActivityRecordRequestDTO requestDTO) {
        Page<PoliceDfActivityRecord> page = policeDfActivityRecordService.pageList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody PoliceDfActivityRecord request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeDfActivityRecordService.save(request));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceDfActivityRecord request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        request.setUpdatedAt(LocalDateTime.now());
        return R.ok(policeDfActivityRecordService.updateById(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody PoliceDfActivityRecord request) {
        return R.ok(policeDfActivityRecordService.removeById(request));
    }
}
