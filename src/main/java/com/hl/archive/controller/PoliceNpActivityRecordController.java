package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceNpActivityRecordQueryDTO;
import com.hl.archive.domain.entity.PoliceNpActivityRecord;
import com.hl.archive.mapper.PoliceNpActivityRecordMapper;
import com.hl.archive.service.PoliceNpActivityRecordService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/npActivityRecord")
@RequiredArgsConstructor
@Api(tags = "新警活动记录")
public class PoliceNpActivityRecordController {

    private final PoliceNpActivityRecordService policeNpActivityRecordService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceNpActivityRecord>> page(@RequestBody PoliceNpActivityRecordQueryDTO request) {
        Page<PoliceNpActivityRecord> page = policeNpActivityRecordService.pageList(request);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody PoliceNpActivityRecord request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeNpActivityRecordService.save(request));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceNpActivityRecord request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeNpActivityRecordService.updateById(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody PoliceNpActivityRecord request) {
        return R.ok(policeNpActivityRecordService.removeById(request));
    }


}
