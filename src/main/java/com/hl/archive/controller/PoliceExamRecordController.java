package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceExamRecordRequestDTO;
import com.hl.archive.domain.dto.PoliceExamRecordBatchAddDTO;
import com.hl.archive.domain.entity.PoliceExamRecord;
import com.hl.archive.service.PoliceExamRecordService;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/examRecord")
@Api(tags = "业务考试模块")
@RequiredArgsConstructor
public class PoliceExamRecordController {

    private  final PoliceExamRecordService policeExamRecordService;

    @PostMapping("/page")
    @ApiOperation("分页")
    public R<List<PoliceExamRecord>> page(@RequestBody PoliceExamRecordRequestDTO requestDTO) {
        Page<PoliceExamRecord> page = policeExamRecordService.pageList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceExamRecord request) {
        return R.ok(policeExamRecordService.updateById(request));
    }

    @PostMapping("/add")
    @ApiOperation("新增")
    public R<Boolean> add(@RequestBody PoliceExamRecord request) {
        if (StrUtil.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(request.getIdCard()));
        }
        return R.ok(policeExamRecordService.save(request));
    }

    @PostMapping("/batchAdd")
    @ApiOperation("批量新增")
    public R<Boolean> batchAdd(@Valid @RequestBody PoliceExamRecordBatchAddDTO request) {
        return R.ok(policeExamRecordService.batchAdd(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody PoliceExamRecord request) {
        return R.ok(policeExamRecordService.removeById(request));
    }


}
