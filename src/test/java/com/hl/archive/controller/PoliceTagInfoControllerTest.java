package com.hl.archive.controller;

import com.alibaba.fastjson2.JSON;
import com.hl.archive.domain.dto.TagDrillDownDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDate;

/**
 * 标签信息控制器测试类
 */
@SpringBootTest
@AutoConfigureTestMvc
public class PoliceTagInfoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    /**
     * 测试标签穿透查询 - 不带时间过滤
     */
    @Test
    public void testTagDrillDownWithoutTimeFilter() throws Exception {
        TagDrillDownDTO request = new TagDrillDownDTO();
        request.setTagType("dengfeng_training");
        request.setPage(1);
        request.setLimit(10);

        mockMvc.perform(MockMvcRequestBuilders.post("/policeTagInfo/tagDrillDown")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试标签穿透查询 - 带时间过滤
     */
    @Test
    public void testTagDrillDownWithTimeFilter() throws Exception {
        TagDrillDownDTO request = new TagDrillDownDTO();
        request.setTagType("dengfeng_training");
        request.setStartDate(LocalDate.of(2023, 1, 1));
        request.setEndDate(LocalDate.of(2023, 12, 31));
        request.setPage(1);
        request.setLimit(10);

        mockMvc.perform(MockMvcRequestBuilders.post("/policeTagInfo/tagDrillDown")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试标签穿透查询 - 只有开始时间
     */
    @Test
    public void testTagDrillDownWithStartDateOnly() throws Exception {
        TagDrillDownDTO request = new TagDrillDownDTO();
        request.setTagType("combat_ability");
        request.setStartDate(LocalDate.of(2023, 6, 1));
        request.setPage(1);
        request.setLimit(10);

        mockMvc.perform(MockMvcRequestBuilders.post("/policeTagInfo/tagDrillDown")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试标签穿透查询 - 只有结束时间
     */
    @Test
    public void testTagDrillDownWithEndDateOnly() throws Exception {
        TagDrillDownDTO request = new TagDrillDownDTO();
        request.setTagType("honors_awards");
        request.setEndDate(LocalDate.of(2023, 12, 31));
        request.setPage(1);
        request.setLimit(10);

        mockMvc.perform(MockMvcRequestBuilders.post("/policeTagInfo/tagDrillDown")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试标签穿透查询 - 星火计划类型带时间过滤
     */
    @Test
    public void testTagDrillDownXinghuoPlanWithTimeFilter() throws Exception {
        TagDrillDownDTO request = new TagDrillDownDTO();
        request.setTagType("xing_huo");
        request.setStartDate(LocalDate.of(2023, 1, 1));
        request.setEndDate(LocalDate.of(2023, 12, 31));
        request.setPage(1);
        request.setLimit(10);

        mockMvc.perform(MockMvcRequestBuilders.post("/policeTagInfo/tagDrillDown")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试标签穿透查询 - 重点关注类型带时间过滤
     */
    @Test
    public void testTagDrillDownKeyFocusWithTimeFilter() throws Exception {
        TagDrillDownDTO request = new TagDrillDownDTO();
        request.setTagType("key_focus");
        request.setStartDate(LocalDate.of(2023, 1, 1));
        request.setEndDate(LocalDate.of(2023, 12, 31));
        request.setPage(1);
        request.setLimit(10);

        mockMvc.perform(MockMvcRequestBuilders.post("/policeTagInfo/tagDrillDown")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }
}
