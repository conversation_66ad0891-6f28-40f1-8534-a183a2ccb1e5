package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBzjlGrry;
import com.hl.orasync.domain.VWjBzjlGrryToPoliceHonorsMapper;
import com.hl.orasync.domain.VWjRybzjlToPoliceHonorsMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__569;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__569.class,
    uses = {ConversionUtils.class,VWjBzjlGrryToPoliceHonorsMapper.class,VWjRybzjlToPoliceHonorsMapper.class,PoliceHonorsToVWjRybzjlMapper.class},
    imports = {}
)
public interface PoliceHonorsToVWjBzjlGrryMapper extends BaseMapper<PoliceHonors, VWjBzjlGrry> {
  @Mapping(
      target = "jljgmc",
      source = "approveAuthority"
  )
  @Mapping(
      target = "bzsj",
      source = "awardDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "dwmc",
      source = "organizationName"
  )
  @Mapping(
      target = "ryjbmc",
      source = "honorLevel"
  )
  @Mapping(
      target = "bzwh",
      source = "awardDocNo"
  )
  @Mapping(
      target = "jlmc",
      source = "honorName"
  )
  @Mapping(
      target = "xxzjbh",
      source = "bh"
  )
  VWjBzjlGrry convert(PoliceHonors source);

  @Mapping(
      target = "jljgmc",
      source = "approveAuthority"
  )
  @Mapping(
      target = "bzsj",
      source = "awardDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "dwmc",
      source = "organizationName"
  )
  @Mapping(
      target = "ryjbmc",
      source = "honorLevel"
  )
  @Mapping(
      target = "bzwh",
      source = "awardDocNo"
  )
  @Mapping(
      target = "jlmc",
      source = "honorName"
  )
  @Mapping(
      target = "xxzjbh",
      source = "bh"
  )
  VWjBzjlGrry convert(PoliceHonors source, @MappingTarget VWjBzjlGrry target);
}
