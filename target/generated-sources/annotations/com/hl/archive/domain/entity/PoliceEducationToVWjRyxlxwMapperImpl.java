package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyxlxw;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-19T15:29:06+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceEducationToVWjRyxlxwMapperImpl implements PoliceEducationToVWjRyxlxwMapper {

    @Override
    public VWjRyxlxw convert(PoliceEducation source) {
        if ( source == null ) {
            return null;
        }

        VWjRyxlxw vWjRyxlxw = new VWjRyxlxw();

        if ( source.getGraduationDate() != null ) {
            vWjRyxlxw.setBysj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getGraduationDate() ) );
        }
        vWjRyxlxw.setXl( source.getEducationLevel() );
        vWjRyxlxw.setGmsfhm( source.getIdCard() );
        if ( source.getEnrollmentDate() != null ) {
            vWjRyxlxw.setRxsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEnrollmentDate() ) );
        }
        vWjRyxlxw.setZymc( source.getMajorName() );
        vWjRyxlxw.setXxmc( source.getSchoolName() );

        return vWjRyxlxw;
    }

    @Override
    public VWjRyxlxw convert(PoliceEducation source, VWjRyxlxw target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getGraduationDate() != null ) {
            target.setBysj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getGraduationDate() ) );
        }
        else {
            target.setBysj( null );
        }
        target.setXl( source.getEducationLevel() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getEnrollmentDate() != null ) {
            target.setRxsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEnrollmentDate() ) );
        }
        else {
            target.setRxsj( null );
        }
        target.setZymc( source.getMajorName() );
        target.setXxmc( source.getSchoolName() );

        return target;
    }
}
