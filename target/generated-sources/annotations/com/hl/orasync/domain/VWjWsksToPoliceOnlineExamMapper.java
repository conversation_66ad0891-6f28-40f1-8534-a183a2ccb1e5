package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOnlineExam;
import com.hl.archive.domain.entity.PoliceOnlineExamToVWjWsksMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__569;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__569.class,
    uses = {ConversionUtils.class,PoliceOnlineExamToVWjWsksMapper.class},
    imports = {}
)
public interface VWjWsksToPoliceOnlineExamMapper extends BaseMapper<VWjWsks, PoliceOnlineExam> {
  @Mapping(
      target = "questionCount",
      source = "tmgs",
      qualifiedByName = {"bigDecimalToInteger"}
  )
  @Mapping(
      target = "score",
      source = "df"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "startTime",
      source = "kssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "endTime",
      source = "jssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "examDuration",
      source = "kssc",
      qualifiedByName = {"bigDecimalToInteger"}
  )
  @Mapping(
      target = "submitStatus",
      source = "sfjj"
  )
  @Mapping(
      target = "examPaperName",
      source = "sjmc"
  )
  PoliceOnlineExam convert(VWjWsks source);

  @Mapping(
      target = "questionCount",
      source = "tmgs",
      qualifiedByName = {"bigDecimalToInteger"}
  )
  @Mapping(
      target = "score",
      source = "df"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "startTime",
      source = "kssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "endTime",
      source = "jssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "examDuration",
      source = "kssc",
      qualifiedByName = {"bigDecimalToInteger"}
  )
  @Mapping(
      target = "submitStatus",
      source = "sfjj"
  )
  @Mapping(
      target = "examPaperName",
      source = "sjmc"
  )
  PoliceOnlineExam convert(VWjWsks source, @MappingTarget PoliceOnlineExam target);
}
